-- Script to rollback XT95683-005
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice "Dummy 2024-1111"
-- Re-insert the deleted accounting_staging record for purchase invoice with _id = '81700'
-- Re-insert the deleted accounting_staging_amount record
-- Re-insert the deleted accounting_staging_line_tax records
-- Re-insert the deleted accounting_staging record

INSERT INTO %%SCHEMA_NAME%%.accounting_staging (
    "_tenant_id",
    "_id",
    batch_id,
    batch_size,
    base_document_line,
    source_base_document_line,
    stock_journal,
    document_sys_id,
    document_number,
    description,
    source_document_number,
    document_date,
    tax_date,
    document_type,
    source_document_type,
    movement_type,
    target_document_type,
    financial_site,
    recipient_site,
    provider_site,
    item,
    account,
    customer,
    supplier,
    pay_to_supplier,
    pay_to_supplier_linked_address,
    return_linked_address,
    item_posting_class,
    customer_posting_class,
    supplier_posting_class,
    resource_posting_class,
    resource,
    transaction_currency,
    company_fx_rate,
    company_fx_rate_divisor,
    payment_term,
    due_date,
    supplier_document_date,
    supplier_document_number,
    is_printed,
    tax_calculation_status,
    fx_rate_date,
    stored_dimensions,
    stored_attributes,
    origin_notification_id,
    stored_computed_attributes,
    is_processed,
    to_be_reprocessed,
    reply_topic,
    analytical_data,
    "_create_user",
    "_update_user",
    "_create_stamp",
    "_update_stamp",
    "_update_tick",
    "_source_id",
    "_custom_data"
  )
VALUES(
    %%TENANT_ID%%,
    317363,
    '401c394c-e60c-4c8d-b230-05a2ea7e13f8',
    1,
    656996,
    NULL,
    NULL,
    81700,
    'Dummy 2024-1111',
    '',
    '',
    '2025-05-13',
    '2025-05-13',
    'purchaseInvoice'::xtrem."finance_document_type_enum",
    'materialTracking'::xtrem."source_document_type_enum",
    'document'::xtrem."movement_type_enum",
    'accountsPayableInvoice'::xtrem."target_document_type_enum",
    180,
    NULL,
    NULL,
    129396,
    NULL,
    NULL,
    23542,
    23542,
    NULL,
    NULL,
    1149,
    NULL,
    NULL,
    NULL,
    NULL,
    28,
    1.**********,
    1.**********,
    221,
    '2025-06-12',
    '2025-05-13',
    'Dummy 2024-1111',
    false,
    NULL,
    '2025-05-13',
    '{}'::jsonb,
    '{"supplier": "AG00001", "stockSite": "WCT", "businessSite": "WCT", "financialSite": "WCT"}'::jsonb,
    'cEyFwQrNakLvBPqMxyOoD',
    '{}'::jsonb,
    false,
    false,
    'PurchaseInvoice/accountingInterface',
    136666,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  );
-- Re-insert the deleted accounting_staging_amount record
INSERT INTO %%SCHEMA_NAME%%.accounting_staging_amount (
    "_tenant_id",
    "_id",
    "_sort_value",
    accounting_staging,
    amount_type,
    amount,
    tax,
    tax_posting_class,
    base_tax,
    document_line_type,
    tax_date,
    tax_rate,
    "_create_user",
    "_update_user",
    "_create_stamp",
    "_update_stamp",
    "_update_tick",
    "_source_id",
    "_custom_data"
  )
VALUES(
    %%TENANT_ID%%,
    503124,
    10,
    317363,
    'amountExcludingTax'::xtrem."amount_type_enum",
    590.**********,
    NULL,
    NULL,
    NULL,
    'documentLine'::xtrem."accounts_payable_receivable_invoice_document_line_type_enum",
    NULL,
    0.**********,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  );
-- Re-insert the deleted accounting_staging_line_tax records
INSERT INTO %%SCHEMA_NAME%%.accounting_staging_line_tax (
    "_tenant_id",
    "_id",
    "_sort_value",
    accounting_staging,
    base_tax,
    "_create_user",
    "_update_user",
    "_create_stamp",
    "_update_stamp",
    "_update_tick",
    "_source_id",
    "_custom_data"
  )
VALUES (
    %%TENANT_ID%%,
    152700,
    10,
    317363,
    983679,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  ),
  (
    %%TENANT_ID%%,
    152701,
    20,
    317363,
    983680,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  ),
  (
    %%TENANT_ID%%,
    152702,
    30,
    317363,
    983681,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  ),
  (
    %%TENANT_ID%%,
    152703,
    40,
    317363,
    983682,
    640,
    645,
    '2025-05-13 23:31:11.300',
    '2025-05-28 00:00:12.732',
    19,
    '',
    '{}'::jsonb
  );
-- Return a message indicating successful rollback
SELECT 'Accounting staging records for purchase invoice "Dummy 2024-1111" with document_sys_id = 81700 have been restored.' AS message;
