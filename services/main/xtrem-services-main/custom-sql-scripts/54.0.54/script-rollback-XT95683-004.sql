-- Script to rollback XT95683-004
-- For issue XT-95683 (BIOCIRCUIT) - Deletion of Purchase Invoice with _id = '81700'
-- This script re-inserts the finance transaction record that was deleted
-- Re-insert the deleted finance transaction record based on backup data
INSERT INTO %%SCHEMA_NAME%%.finance_transaction (
    "_tenant_id",
    "_id",
    batch_id,
    document_number,
    document_sys_id,
    document_type,
    target_document_type,
    target_document_number,
    target_document_sys_id,
    source_document_type,
    source_document_number,
    source_document_sys_id,
    financial_site,
    status,
    finance_integration_app,
    finance_integration_app_record_id,
    finance_integration_app_url,
    message,
    last_status_update,
    "_create_user",
    "_update_user",
    "_create_stamp",
    "_update_stamp",
    "_update_tick",
    "_source_id",
    "_custom_data"
  )
VALUES(
    %%TENANT_ID%%,
    '125830',
    '401c394c-e60c-4c8d-b230-05a2ea7e13f8',
    'Dummy 2024-1111',
    '81700',
    'purchaseInvoice'::xtrem."finance_document_type_enum",
    'accountsPayableInvoice'::xtrem."target_document_type_enum",
    '',
    0,
    'materialTracking'::xtrem."source_document_type_enum",
    '',
    0,
    180,
    'notRecorded'::xtrem."finance_integration_status_enum",
    NULL,
    '',
    '',
    '[{"type":3,"message":{"message":"value too long for type character varying(30)","extensions":{"code":"system-error"}}}]',
    '2025-05-13 23:31:14+00'::timestamp with time zone,
    640,
    645,
    '2025-05-13 23:31:11.3+00'::timestamp with time zone,
    '2025-05-28 00:00:12.732+00'::timestamp with time zone,
    19,
    '',
    '{}'::jsonb
  );
-- Return a message indicating successful rollback
SELECT 'Finance transaction record with _id = ''125830'' for purchase invoice 81700 has been restored.' AS message;
